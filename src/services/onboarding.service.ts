import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {OrganizationRepository, OrganizationPlanRepository} from '../repositories';
import { getURL } from '../utils';
const fetch = require('node-fetch');

const ONBOARD_URL = process.env.ONBOARDING_AWS_URL || 'ojpurdnz1b.execute-api.us-east-1.amazonaws.com';

@injectable({scope: BindingScope.TRANSIENT})
export class OnboardingService {
  constructor(
    @repository(OrganizationRepository)
    private organizationRepository: OrganizationRepository,
    @repository(OrganizationPlanRepository)
    private organizationPlanRepository: OrganizationPlanRepository,
  ) {}

  private async verifyDomainReachability(domain: string): Promise<{reachable: boolean, error?: string}> {
    // Clean the domain - extract just the domain part from any URL format
    let cleanDomain = domain.trim();

    // Remove protocol if present
    cleanDomain = cleanDomain.replace(/^https?:\/\//, '');
    // Remove www prefix for testing
    cleanDomain = cleanDomain.replace(/^www\./, '');
    // Remove any path, query params, or fragments
    cleanDomain = cleanDomain.split('/')[0].split('?')[0].split('#')[0];

    // Test multiple URL variations to ensure the domain is reachable
    const urlsToTest = [
      `https://${cleanDomain}`,
      `https://www.${cleanDomain}`,
      `http://${cleanDomain}`,
      `http://www.${cleanDomain}`
    ];

    for (const testUrl of urlsToTest) {
      try {
        console.log(`Testing domain reachability: ${testUrl}`);
        const response = await fetch(testUrl, {
          method: 'HEAD',
          timeout: 10000, // 10 second timeout
          redirect: 'follow',
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; Raleon-Bot/1.0)'
          }
        });

        // Consider 2xx, 3xx, and even some 4xx responses as "reachable"
        // since they indicate the domain exists and responds
        if (response.status < 500) {
          console.log(`Domain ${cleanDomain} is reachable via ${testUrl} (status: ${response.status})`);
          return { reachable: true };
        }
      } catch (error) {
        console.log(`Failed to reach ${testUrl}:`, error.message);
        // Continue to next URL variant
      }
    }

    return {
      reachable: false,
      error: `Unable to verify domain "${cleanDomain}" is publicly accessible. Please ensure the domain is correct and publicly available.`
    };
  }

  async onboardBegin(body: any, orgId: number) {
	if (!body.externalDomain) {
      await this.organizationRepository.findOne({
        where: {
          id: orgId
        }
      }).then((org) => {
        body.externalDomain = org?.externalDomain;
      });
    }

	if (!body.externalDomain) {
      return {
        statusCode: 400,
        body: {
          error: 'No external domain provided'
        }
      }
    }

    // Verify domain is reachable
    const reachabilityCheck = await this.verifyDomainReachability(body.externalDomain);
    if (!reachabilityCheck.reachable) {
      return {
        statusCode: 400,
        body: {
          error: reachabilityCheck.error || 'Domain is not publicly accessible'
        }
      }
    }

    const url = body.externalDomain.startsWith('http')
      ? body.externalDomain
      : `https://${body.externalDomain}`;

    // Save orgType if provided
    if (body.orgType) {
      await this.organizationRepository.updateById(orgId, {
        orgType: body.orgType
      });

      // If orgType is 'agency', change plan from 15 to 16
      if (body.orgType === 'agency') {
        const currentPlan = await this.organizationPlanRepository.findOne({
          where: { orgId: orgId, status: 'ACTIVE' }
        });

        if (currentPlan && currentPlan.planId === 15) {
          await this.organizationPlanRepository.updateById(currentPlan.id, {
            planId: 16
          });
        }
      }
    }

    let payload = {
      organization_id: orgId,
      url
    }
    let response: any;
    try {
      const url = `/prod/onboard`;
      const signedRequest = getURL(url, 'POST', payload, ONBOARD_URL);
      response = await fetch(`https://${ONBOARD_URL}${url}`, signedRequest);
    } catch (err) {
      console.log("Error: " + JSON.stringify(err));
      throw new Error("Error: " + JSON.stringify(err));
    }

    const data: any = await response.json();

    return {
      statusCode: 200,
      body: {
        orgId: orgId,
        externalDomain: body.externalDomain,
        data: data
      }
    }
  }
}
