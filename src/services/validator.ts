import {HttpErrors} from '@loopback/rest';
import isemail from 'isemail';
import {KeyAndPassword} from '../models/user-management';
import {Credentials} from '../repositories';

export function validateCredentials(credentials: Credentials) {
  // Validate Email
  if (!isemail.validate(credentials.email)) {
    throw new HttpErrors.UnprocessableEntity('invalid email');
  }

  // Validate Password Length
  if (!credentials.password || credentials.password.length < 8) {
    throw new HttpErrors.UnprocessableEntity(
      'password must be minimum 8 characters',
    );
  }
}

export function validateKeyPassword(keyAndPassword: KeyAndPassword) {
  // Validate Password Length
  if (!keyAndPassword.password || keyAndPassword.password.length < 8) {
    throw new HttpErrors.UnprocessableEntity(
      'password must be minimum 8 characters',
    );
  }

  if (keyAndPassword.password !== keyAndPassword.confirmPassword) {
    throw new HttpErrors.UnprocessableEntity(
      'password and confirmation password do not match',
    );
  }

  if (
    keyAndPassword.resetKey.length === 0 ||
    keyAndPassword.resetKey.trim() === ''
  ) {
    throw new HttpErrors.UnprocessableEntity('reset key is mandatory');
  }
}

export function validateDomain(domain: string): string {
  if (!domain || typeof domain !== 'string') {
    throw new HttpErrors.UnprocessableEntity('Domain is required');
  }

  // Extract domain from URL (handle full URLs with paths)
  let inputUrl = domain.trim();
  let cleanDomain = '';

  try {
    // Try to parse as a full URL first
    const urlObj = new URL(inputUrl.startsWith('http') ? inputUrl : `https://${inputUrl}`);
    cleanDomain = urlObj.hostname;
  } catch {
    // If URL parsing fails, try to extract domain manually
    cleanDomain = inputUrl
      .replace(/^https?:\/\//, '') // Remove protocol
      .replace(/^www\./, '') // Remove www prefix
      .split('/')[0] // Take only the domain part (before any path)
      .split('?')[0] // Remove query parameters
      .split('#')[0]; // Remove fragments
  }

  // Validate domain format - same pattern as frontend
  const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/;
  if (!domainPattern.test(cleanDomain)) {
    throw new HttpErrors.UnprocessableEntity(
      'Invalid domain format. Please provide a valid domain (e.g., example.com)'
    );
  }

  return cleanDomain;
}
